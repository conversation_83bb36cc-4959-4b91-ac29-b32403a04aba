<template>
  <div class="q-opt q-item-box" :id="item.id" @mouseover="onMainOver" @mouseout="onMainOut"
    :class="['q-item-box' + item.typeId, Paper.isEdit ? 'uneditstyle' : '', showErrorBox && 'error-tip', (Paper.ansToTopConfig.object || Paper.ansType == ANSWER_TYPE.write) && 'fontSizeTag']">
    <div class="edit nosave" @click="editQues"></div>
    <div class="pop up nosave" v-if="item.typeId == QUES_TYPE.singleChoice || item.typeId == QUES_TYPE.choice
    ">
      <el-radio-group v-model="ques.qsList[0].showType" @change="showChange">
        <el-radio :label="3">一行四个</el-radio>
        <el-radio :label="2">一行两个</el-radio>
        <el-radio :label="1">一行一个</el-radio>
        <el-radio v-if="Paper.isEnglishSubject()" :label="0">一行显示</el-radio>
      </el-radio-group>
    </div>
    <div ref="qWrapRef" :class="[`op${ques.qsList[0].showType}`, { drag: isDrag }]" @dblclick="resetMath">
      <!-- 保存后还原题目数据展示 -->
      <template v-if="ques.qsList[0].editContent">
        <div class="ques-box" :style="{ height: height }" :id="'ck_' + item.id" :typeid="item.typeId"
          v-html="converMathtex(ques.qsList[0].editContent)"></div>
      </template>

      <template v-else>
        <!-- 主观题 -->
        <template v-if="item.typeId == QUES_TYPE.subject">
          <div class="ques-box" :style="{ height: height }" :id="'ck_' + item.id" :typeid="item.typeId">
            <div v-if="ques.qsList[0].tmpcontent" class="tmpcontent" v-html="convertContent(ques.qsList[0].tmpcontent, item.typeId, false)
              "></div>
            <div class="hascontent" v-html="convertContent(ques.qsList[0].content, item.typeId)">
            </div>
          </div>
        </template>

        <!-- 选择题 -->

        <template v-else-if="isChoiceQuesType">
          <div class="ques-box" :id="'ck_' + item.id" :style="{ height: height }" :typeid="item.typeId">
            <!-- 题干 -->
            <div v-if="ques.qsList[0].tmpcontent" class="tmpcontent" v-html="convertContent(ques.qsList[0].tmpcontent, item.typeId, false)
              "></div>

            <p :class="['ques-content', hasTextorImg(ques.qsList[0].content) ? '' : 'left']"
              v-html="convertContent(ques.qsList[0].content, item.typeId, true, true)" v-if="!keeppt"></p>
            <div :class="['ques-content', hasTextorImg(ques.qsList[0].content) ? '' : 'left']"
              v-html="convertContent(ques.qsList[0].content, item.typeId, true, true)" v-else></div>

            <!-- 选择题选项 -->
            <div :class="['q-r-option', hasTextorImg(ques.qsList[0].content) ? '' : 'has-none-title']">
              <span class="q-r-o-item" v-for="(oitem, index) in ques.qsList[0].optionText" :key="index">
                <span :class="[
                  'ocr-pos',
                  item.answer && (item.answer.indexOf(az[index]) >= 0 || item.answer[0].indexOf(az[index]) >= 0)
                    ? 'isAns'
                    : '',
                ]">
                  [<span style="padding: 0 0.6mm;margin: 0;">{{ az[index] }}</span>]
                </span>
                <span class="ques-option" v-html="convertOption(oitem)"></span>
              </span>
            </div>
          </div>
        </template>
        <!-- 判断题 -->

        <template v-else-if="item.typeId == QUES_TYPE.judge">
          <div class="ques-box" :id="'ck_' + item.id" :style="{ height: height }" :typeid="item.typeId">
            <!-- 题干 -->
            <div v-if="ques.qsList[0].tmpcontent" class="tmpcontent" v-html="convertContent(ques.qsList[0].tmpcontent, item.typeId, false)
              "></div>
            <p class="ques-content" v-html="convertContent(ques.qsList[0].content, item.typeId, true, true)"></p>
            <!-- 选项 -->
            <div class="q-r-option">
              <span class="q-r-o-item" v-for="(oitem, index) in (new Array('A', 'B'))" :key="index">
                <span :class="[
                  'ocr-pos',
                  item.answer && item.answer.indexOf(az[index]) >= 0
                    ? 'isAns'
                    : '',
                ]">
                  [<span style="padding: 0 0.6mm;margin: 0;">{{ az[index] }}</span>]
                </span>
              </span>
            </div>
          </div>
        </template>

        <!-- 填空题 -->

        <template v-else>
          <div v-if="!asyncShow" class="ques-box" :id="'ck_' + item.id" :style="{ height: height }"
            :typeid="item.typeId">
            <div v-if="ques.qsList[0].tmpcontent" class="tmpcontent" v-html="convertContent(ques.qsList[0].tmpcontent, item.typeId, false)
              "></div>
            <div class="hascontent">
              <span class="noeditsave ques-num">{{ item.quesNos }}.</span>
              <span class="fill-content" v-html="convertContent(ques.qsList[0].content, item.typeId)"></span>
              <!-- <p class="fill-content"></p> -->
              <!-- <div ref="qContentRef"></div> -->
            </div>
          </div>
        </template>
      </template>
    </div>

    <div class="pop down nosave">
      <div class="pop-icon" @click="setHeight('half')" title="点击调整至页面一半">
        <span class="half"></span>
      </div>
      <div class="pop-icon" @click="setHeight('full')" title="点击调整至页面底部">
        <span class="bottom"></span>
      </div>
      <div class="pop-icon pop-icon-free" @mousedown="startDrag" title="拖动鼠标调整高度">
        <span class="free"></span>
      </div>
    </div>

  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  watch,
  nextTick,
  h,
  onUnmounted,
  render,
  getCurrentInstance,
  ComponentInternalInstance,
  onUpdated,
  onRenderTriggered,
} from 'vue';
import { arabToChinese, getQueryString } from '@/utils/util';
import Paper from '../../src/views/paper';
import {
  DECIMAL_TYPE,
  GRIDPLACE_TYPE,
  GRID_TYPE,
  ICorrectType,
  IPAGELAYOUT,
  JUDGE_TYPE,
  MARK_TYPE,
  QUES_TYPE,
  ANSWER_TYPE,
  QUES_SCAN_MODE
} from '@/typings/card';
import MarkScore from './MarkScore.vue';
import { getHeight, getWidth, pxConversionMm, reloadMathtex } from '../utils/dom';
import {
  pageHeight,
  footerKeepHeight,
  headerInfoHeight,
  getHeaderInfoHeight,
} from '@/utils/config';
import bus from '@/utils/bus';
import { PaperConstant } from '@/views/paper.constant';
import { ElMessage } from 'element-plus';

export default defineComponent({
  props: {
    //题干信息
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    //题目
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
    //大题题号
    index: {
      type: Number,
      default: 0,
    },
  },
  components: { MarkScore },
  setup(props, ctx) {
    if (!Paper.questions[props.item.id]) {
      console.warn('Paper.questions', props.item.id, Paper.questions);
    }
    const instance = getCurrentInstance() as ComponentInternalInstance | null;
    const state = reactive({
      QUES_TYPE: QUES_TYPE,
      ANSWER_TYPE,
      GRIDPLACE_TYPE: GRIDPLACE_TYPE,
      JUDGE_TYPE: JUDGE_TYPE,
      MARK_TYPE: MARK_TYPE,
      QUES_SCAN_MODE: QUES_SCAN_MODE,
      GRID_TYPE: GRID_TYPE,
      DECIMAL_TYPE: DECIMAL_TYPE,
      Paper: Paper,
      ques: Paper.questions[props.item.id],
      isOver: false,
      isFocus: false,
      isDrag: false,
      isEdit: false,
      az: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      height: '',
      showType: 1,
      asyncShow: false,
      // 是否选择题型
      isChoiceQuesType:
        props.item.typeId == QUES_TYPE.singleChoice || props.item.typeId == QUES_TYPE.choice,
      initHeight: 0,
      // 显示创建作文格弹窗
      showCreateCellsDialog: false,
      editor: null,
      // 当前卡片所处的布局
      isA33Layout: false,
      showErrorBox: false,
      // 是否保持p标签
      keeppt: getQueryString("keeppt") == 1,
    });

    watch(
      () => props.item.answer,
      () => {
        if (!state.isChoiceQuesType) return;
        //监听答案变化
        const els = document.querySelectorAll(`[id="${props.item.id}"]`);
        const el = els[els.length - 1];
        Array.from(el.getElementsByClassName('q-r-o-item')).forEach((opt: any, index: number) => {
          opt.getElementsByClassName('ocr-pos')[0].className = opt
            .getElementsByClassName('ocr-pos')[0]
            .className.replace('isAns', '');
        });
        props.item.answer.split(/,|，| |/).forEach((ans: string) => {
          if (!ans) return;
          el.getElementsByClassName('q-r-o-item')
          [ans.toLocaleUpperCase().charCodeAt(0) - 65].getElementsByClassName('ocr-pos')[0]
            .classList.add('isAns');
        });
      }
    );
    watch([() => props.info, () => props.item.score], () => {
      //监听分值变化，替换题目内容中分值
      // const sdom = document
      //   .getElementById("ck_" + props.item.id)
      //   ?.getElementsByClassName("item-score")[0];
      // sdom && (sdom.textContent = props.item.score);
      nextTick(() => {
        renderScore();
      });
    });
    watch([() => props.info.markType, () => props.item.scanMode], () => {
      nextTick(() => {
        scanFill(false);
      });
    });
    watch([() => props.item.quesNos], (newV, oldV) => {
      const els = document.querySelectorAll(`[id="${props.item.id}"]`);
      Array.from(els).forEach(el => {
        const ndom = el?.getElementsByClassName('ques-num')[0];
        ndom && (ndom.innerHTML = ndom.innerHTML.replace(oldV + '.', newV + '.'));
      });
    });
    const qWrapRef = ref<any>(null);
    const qContentRef = ref<any>(null);

    const convertSort = (sort: number) => {
      return arabToChinese(sort);
    };
    //是否包含文本或图片标签
    const hasTextorImg = (str: string) => {
      str = removeNonClosedTags(str);
      const doc = document.createElement('div');
      doc.innerHTML = str;
      return doc.getElementsByTagName('img').length > 0 || doc.innerText.replace(/ /g, '') != '';
    };
    /**
     * @description: 确认创建作文格
     * @return {*}
     */
    const confirmCreatCells = async () => {
      state.showCreateCellsDialog = false;
      // let isUpdated = await checkUpdateSubjectHeight();
      // if (isUpdated) bus.emit('renderAllCard');
    };

    const removeNonClosedTags = (content: string) => {
      const _div = document.createElement('div');
      _div.innerHTML = content;
      function traverse(node: any) {
        if (node.nodeType == 3) {
          node.textContent = node.textContent
            .replace(/\n/g, ' ')
            .replace(/\\\n/g, ' ')
            .replace(/\n /g, ' ');
          if (node.textContent.replace(/ /g, '') == '') return;
          // Node.TEXT_NODE
          let newNode = document.createElement('span');
          newNode.innerText = node.textContent;
          node.parentElement.replaceChild(newNode, node);
        } else if (node.nodeType == 1 && node.nodeName != 'SPAN' && node.nodeName != 'U') {
          if (node.nodeName == 'P' && node.childElementCount == 0 && node.textContent == '') {
            node.parentElement.removeChild(node);
          }
          // Node.ELEMENT_NODE
          Array.from(node.childNodes).forEach(n => {
            traverse(n);
          });
        }
      }
      traverse(_div);
      return _div.innerHTML;
    };
    const converMathtex = (content: string) => {
      if (Paper.isEnglishSubject()) return content;
      if (content.indexOf('ck-math-tex') < 0) {
        //移除换行标签，避免正则识别失效，
        content = content
          .replace(/\n/g, ' ')
          .replace(/\\\n/g, ' ')
          .replace(
            / *\\\((.*?)\\\) */g,
            '<span tex="\\($1\\)" title="双击编辑公式" contenteditable="false" class="ck-math-tex">\\($1\\)</span>'
          )
          .replace(
            / *\\\[(.*?)\\\] */g,
            '<span tex="\\($1\\)" title="双击编辑公式" contenteditable="false" class="ck-math-tex">\\($1\\)</span>'
          )
          .replace(
            / *\$\$(.*?)\$\$ */g,
            '<span tex="\\($1\\)" title="双击编辑公式" contenteditable="false" class="ck-math-tex">\\($1\\)</span>'
          )
          .replace(
            / *\$(.*?)\$ */g,
            '<span tex="\\($1\\)" title="双击编辑公式" contenteditable="false" class="ck-math-tex">\\($1\\)</span>'
          );
      }
      return content;
    };
    const convertContent = (content: string, typeid?: QUES_TYPE, hasNo = true, noPTag = false) => {
      content = converMathtex(content);
      content = removeNonClosedTags(content);
      if (noPTag && !state.keeppt) {
        content = content.replace(/<p(.)*?>/g, '').replace(/<\/p>/g, '');
      }
      content = content
        .replace(/<tr(.)*?>/g, '<tr>') //去除表格tr属性
        .replace(/<br>/g, '')
        .replace(/<p>[ ]*?<\/p>/g, '')
        .replace(/<div(.)*?>/g, '')
        .replace(/<\/div>/g, '')
        .replace(/<!--(.)*?>/g, '');
      content = content.replace(/<\/?[a](.)*?\/?>/g, '');

      // content = content.replace(
      //   /<u(.)*?>/g,
      //   "<u style='font-family:Times New Roman'>"
      // );

      //主观题动态增加题号
      if (typeid == QUES_TYPE.subject) {
        if (hasNo) {
          if (/^<p/.test(content)) {
            content = content.replace(
              '<p',
              `<p><span class="noeditsave ques-num">${props.item.quesNos}.</span`
            );
          } else {
            content = `<span class="noeditsave ques-num">${props.item.quesNos}.</span>${content}`;
          }
        }
      } else {
        if (
          (typeid == QUES_TYPE.choice ||
            typeid == QUES_TYPE.singleChoice ||
            typeid == QUES_TYPE.judge) &&
          hasNo
        ) {
          if (/^<span((?!ck-math-tex).)*?>/.test(content)) {
            content = content.replace(
              /^<span((?!ck-math-tex).)*?>/,
              `<span class="noeditsave ques-num">${props.item.quesNos}.`
            );
          } else if (/^<p/.test(content)) {
            content = content.replace(
              '<p',
              `<p><span class="noeditsave ques-num">${props.item.quesNos}.</span`
            );
          } else {
            content = `<span class="noeditsave ques-num">${props.item.quesNos}.</span>${content}`;
          }
        } else if (Paper.isFillQues(typeid)) {
          content = content.replace(/<\/u><u(.)*?>/g, '');
          if (/^<p/.test(content)) {
            //移除第一个p标签，确保不会换行
            content = content.replace('<p', '<span').replace('</p>', '</span>');
          }
        }
      }
      return content;
    };
    const convertOption = (opt: string) => {
      opt = converMathtex(opt);
      //移除所有<p></p>标签
      opt = opt
        .replace(/<\/?p>/g, '')
        .replace(/<p(.)*?>/g, '')
        .replace(/<\/p>/g, '')
        .replace(/<br>/g, '')
        .replace(/<sub>(.*?)<\/sub>/g, function (str, $1) {
          return str.replace($1, $1.trim());
        })
        .replace(/<sup>(.*?)<\/sup>/g, function (str, $1) {
          return str.replace($1, $1.trim());
        })
        .trim();
      return opt;
    };
    const renderOption = () => {
      // 参数验证和边界条件检查
      if (!props.item?.id || !state.ques?.qsList?.[0]) {
        console.warn('renderOption: 缺少必要参数', { itemId: props.item?.id, qsList: state.ques?.qsList });
        return;
      }

      // 题型验证
      if (
        props.item.typeId != QUES_TYPE.choice &&
        props.item.typeId != QUES_TYPE.singleChoice &&
        props.item.typeId != QUES_TYPE.judge
      ) {
        return;
      }

      // 完成制卡后不再计算选项位置，保证位置与保存一致
      if (Paper.isEdit) return;

      // 性能优化：添加渲染前的状态检查
      const currentShowType = state.ques.qsList[0].showType;
      const renderKey = `${props.item.id}_${currentShowType}_${props.info.judgeType || 1}`;

      // DOM查询和验证
      let dom: HTMLElement = null;
      try {
        const els = document.querySelectorAll(`[id="${props.item.id}"]`);
        if (els.length > 1) {
          // 在跨页分割的情况下，查找包含选项的DOM
          for (const el of els) {
            if (el.getElementsByClassName('q-r-option')[0]) {
              dom = el as HTMLElement;
              break;
            }
          }
        } else {
          dom = getCurQuesDom(props.item.id);
        }

        // DOM存在性和连接性检查
        if (!dom || !dom.isConnected) {
          console.warn('renderOption: 目标DOM不存在或已断开连接', { itemId: props.item.id });
          return;
        }
      } catch (error) {
        console.error('renderOption: DOM查询失败', error);
        return;
      }

      // 判断题特殊处理
      if (props.item.typeId == QUES_TYPE.judge) {
        try {
          const opts = dom.getElementsByClassName('ocr-pos');
          if (opts.length === 0) {
            console.warn('renderOption: 判断题缺少选项元素', { itemId: props.item.id });
            return;
          }

          const judgeType = props.info.judgeType || 1;
          const tfNames = PaperConstant.TFNameList[judgeType];

          if (!tfNames) {
            console.warn('renderOption: 无效的判断题类型', { judgeType });
            return;
          }

          Array.from(opts).forEach((opt, index) => {
            try {
              // 安全检查：确保子元素存在
              if (!opt.children[0]) {
                console.warn('renderOption: 判断题选项缺少子元素', { index });
                return;
              }

              opt.children[0].innerHTML = tfNames[index] || '';

              // 根据配置显示或隐藏选项
              const parentElement = opt.parentElement;
              if (parentElement) {
                parentElement.style.display = Paper.ansToTopConfig.object ? 'none' : 'unset';
              }
            } catch (error) {
              console.error('renderOption: 判断题选项处理失败', { index, error });
            }
          });

          return;
        } catch (error) {
          console.error('renderOption: 判断题渲染失败', error);
          return;
        }
      }
      // 选择题DOM重构逻辑
      let reconstructionSuccess = false;
      try {
        // 获取或创建选项容器
        let newEl: HTMLElement;
        const existingOptionContainer = dom.getElementsByClassName('q-r-option')[0];
        if (existingOptionContainer) {
          newEl = existingOptionContainer.cloneNode(false) as HTMLElement;
        } else {
          newEl = document.createElement("div");
          newEl.className = "q-r-option";
        }

        // 获取题目容器
        const optionsEl = dom.getElementsByClassName('ques-box')[0];
        if (!optionsEl) {
          console.warn('renderOption: 缺少题目容器元素', { itemId: props.item.id });
          return;
        }

        const letters: HTMLElement[] = [];
        const doms: HTMLElement[] = [];

        // 查找选项标签及选项内容
        const qOptsEls = $(".q-r-option", dom);

        // 使用静态副本进行DOM遍历，避免在循环中修改集合
        Array.from(qOptsEls).forEach((qEl) => {
          // 创建childNodes的静态副本
          const itemOptsEls = Array.from(qEl.childNodes);

          itemOptsEls.forEach((itemEl: Node) => {
            if (!(itemEl instanceof HTMLElement)) return;

            if (itemEl.childNodes.length) {
              // 创建childNodes的静态副本，避免在遍历中修改
              const childNodes = Array.from(itemEl.childNodes);

              childNodes.forEach((el: Node) => {
                if (!(el instanceof HTMLElement) && el.nodeType !== Node.TEXT_NODE) return;

                try {
                  if (el.nodeType === Node.ELEMENT_NODE &&
                      (el as HTMLElement).classList?.contains('ocr-pos')) {
                    // 选项标识符处理
                    const newOptionEl = document.createElement("span");
                    newOptionEl.className = "ques-option";
                    letters.push(el as HTMLElement);
                    doms.push(newOptionEl);
                  } else {
                    // 选项内容处理
                    let htmlContent = '';
                    if (el.nodeName === "#text") {
                      htmlContent = el.textContent || '';
                    } else if ((el as HTMLElement).classList?.contains('ques-option')) {
                      htmlContent = (el as HTMLElement).innerHTML;
                    } else if ((el as HTMLElement).classList?.contains('opt-space')) {
                      htmlContent = (el as HTMLElement).innerHTML.replace(/[\u200B-\u200D\uFEFF]/g, '');
                    } else {
                      htmlContent = (el as HTMLElement)?.outerHTML || "";
                    }

                    // 安全地添加内容到最后一个选项
                    if (doms.length > 0) {
                      doms[doms.length - 1].innerHTML += htmlContent;
                    }
                  }

                  // 安全地移除元素
                  if (el.parentElement && el.parentElement.contains(el)) {
                    el.parentElement.removeChild(el);
                  }
                } catch (elementError) {
                  console.warn('renderOption: 处理选项子元素失败', { elementError });
                }
              });
            } else {
              // 处理没有子节点的元素
              try {
                let content = '';
                if (itemEl.nodeName === "#text") {
                  content = itemEl.textContent || '';
                } else {
                  content = itemEl.outerHTML || '';
                }

                if (doms.length > 0) {
                  doms[doms.length - 1].innerHTML += content;
                }

                // 安全地移除元素
                if (itemEl.parentElement && itemEl.parentElement.contains(itemEl)) {
                  itemEl.parentElement.removeChild(itemEl);
                }
              } catch (elementError) {
                console.warn('renderOption: 处理选项元素失败', { elementError });
              }
            }
          });
        });

        // 移除旧的选项容器
        $('.q-r-option', dom).remove();

        // 构造新的选项结构
        letters.forEach((letter, index) => {
          try {
            const itemEl = document.createElement("span");
            itemEl.className = "q-r-o-item";
            itemEl.appendChild(letter);

            if (doms[index]) {
              itemEl.appendChild(doms[index]);
            }

            newEl.appendChild(itemEl);
          } catch (itemError) {
            console.warn('renderOption: 构造选项项失败', { index, itemError });
          }
        });

        // 插入新的选项容器
        optionsEl.appendChild(newEl);
        reconstructionSuccess = true;

      } catch (error) {
        console.error('renderOption: DOM重构失败', error);

        // 降级方案：尝试恢复基本的选项结构
        try {
          restoreBasicOptionStructure(dom);
        } catch (fallbackError) {
          console.error('renderOption: 降级方案也失败', fallbackError);
          return;
        }
      }

      // 如果重构失败，不继续后续处理
      if (!reconstructionSuccess) {
        console.warn('renderOption: DOM重构未成功，跳过后续处理');
        return;
      }

      // 布局计算和样式应用
      try {
        const hasNum = dom.getElementsByClassName('has-none-title').length;
        const showType = state.ques.qsList[0].showType;

        // 宽度计算边界检查
        let boxW = getWidth(dom);
        if (boxW <= 0) {
          console.warn('renderOption: 容器宽度无效', { boxW, itemId: props.item.id });
          return;
        }

        // 减去padding和分辨率冗余
        boxW = boxW - 2 - 4;
        if (hasNum) {
          // 如果包含无内容题号，减去题号宽度
          boxW = boxW - 5;
        }

        // 最小宽度保护
        if (boxW < 10) {
          console.warn('renderOption: 计算后宽度过小，跳过渲染', { boxW });
          return;
        }

        // 根据显示类型计算单个选项宽度
        const validShowTypes = ['0', '1', '2', '3', '4'];
        debugger
        if (!validShowTypes.includes(String(showType))) {
          console.warn('renderOption: 无效的显示类型', { showType });
          return;
        }

        if (showType == '1') {
          boxW = boxW / 1;
        } else if (showType == '2') {
          boxW = boxW / 2;
        } else if (showType == '3' || showType == '4') {
          boxW = boxW / 4;
        }

        // 清理旧的间距元素
        const oldSpaceElements = Array.from(dom.getElementsByClassName('opt-space'));
        oldSpaceElements.forEach((item: Element) => {
          try {
            const parent = item.parentElement;
            if (parent && parent.contains(item)) {
              parent.removeChild(item);
            }
          } catch (removeError) {
            console.warn('renderOption: 清理旧间距元素失败', removeError);
          }
        });

        // 如果是一行显示模式，直接返回
        if (showType == '0') {
          return;
        }

        // 处理选项样式和间距
        const opts = dom.getElementsByClassName('ocr-pos');
        if (opts.length === 0) {
          console.warn('renderOption: 没有找到选项元素');
          return;
        }

        Array.from(opts).forEach((opt) => {
          try {
            // 安全检查：确保firstElementChild存在
            if (!opt.firstElementChild) {
              console.warn('renderOption: 选项缺少子元素');
              return;
            }

            // 根据配置设置选项样式
            if (Paper.ansToTopConfig.object || Paper.ansType == ANSWER_TYPE.write) {
              opt.innerHTML = `${opt.firstElementChild.outerHTML}.`;
              opt.classList.add('n-opts');
            } else {
              opt.innerHTML = `[${opt.firstElementChild.outerHTML}]`;
              opt.classList.remove('n-opts');
            }

            // 计算并添加间距
            const parentElement = opt.parentElement;
            if (!parentElement) {
              console.warn('renderOption: 选项缺少父元素');
              return;
            }

            let width = boxW - getWidth(parentElement);

            // 处理负宽度的边界情况
            if (width < 0) {
              console.warn('renderOption: 计算宽度为负值，使用绝对值', { width, boxW });
              width = Math.abs(width);
            }

            // 限制最大间距，避免布局异常
            // width = Math.min(width, boxW * 0.8);

            // 生成间距HTML
            let nbspStr = '';
            // const fullSpaceCount = Math.floor(width / 2);
            // const remainderWidth = width % 2;

            for (let i = 0; i < Math.floor(width / 2); i++) {
              nbspStr += `<span class='opt-space' style="display:inline-block;min-width:2mm;">&ZeroWidthSpace;</span>`;
            }
            nbspStr += `<span class='opt-space' style="display:inline-block;min-width:${width % 2}mm;">&ZeroWidthSpace;</span>`;

            // 安全地插入间距HTML
            if (nbspStr) {
              parentElement.insertAdjacentHTML('beforeend', nbspStr);
            }

          } catch (optError) {
            console.error('renderOption: 处理单个选项失败', optError);
          }
        });


      } catch (layoutError) {
        console.error('renderOption: 布局计算失败', layoutError);
      }
    };

    // 降级方案：恢复基本的选项结构
    const restoreBasicOptionStructure = (dom: HTMLElement) => {
      try {
        const optionsEl = dom.getElementsByClassName('ques-box')[0];
        if (!optionsEl) return;

        // 创建基本的选项容器
        const basicContainer = document.createElement('div');
        basicContainer.className = 'q-r-option';

        // 创建基本的A、B选项（适用于判断题）
        const letters = ['A', 'B'];
        letters.forEach((letter) => {
          const itemEl = document.createElement('span');
          itemEl.className = 'q-r-o-item';

          const ocrEl = document.createElement('span');
          ocrEl.className = 'ocr-pos';

          const letterEl = document.createElement('span');
          letterEl.textContent = letter;
          ocrEl.appendChild(letterEl);

          itemEl.appendChild(ocrEl);
          basicContainer.appendChild(itemEl);
        });

        // 移除旧的选项容器
        const oldContainers = dom.getElementsByClassName('q-r-option');
        Array.from(oldContainers).forEach(container => {
          if (container.parentElement) {
            container.parentElement.removeChild(container);
          }
        });

        // 插入新的基本容器
        optionsEl.appendChild(basicContainer);

        console.info('renderOption: 已恢复基本选项结构');
      } catch (error) {
        console.error('restoreBasicOptionStructure: 恢复失败', error);
        throw error;
      }
    };
    const renderScore = () => {
      if (
        props.item.typeId == QUES_TYPE.choice ||
        props.item.typeId == QUES_TYPE.singleChoice ||
        props.item.typeId == QUES_TYPE.judge
      ) {
        renderOption();
        return;
      }
      const splitEls = document.querySelectorAll(`[id="${props.item.id}"]`);
      let quesEl: any = null;
      Array.from(splitEls).forEach((item: any) => {
        item?.getElementsByClassName('score-container')[0]?.remove();
        Array.from(item?.getElementsByClassName('score-option-item')).forEach((opt: any) => {
          opt?.remove();
        });
        if (props.item.typeId == QUES_TYPE.subject) {
          if (item.getElementsByClassName('ques-box').length) {
            if (props.info.markType == MARK_TYPE.YESORNO) {
              quesEl = item;
            } else {
              !quesEl && (quesEl = item);
            }
          }
        } else {
          if (item.querySelectorAll('[class^=hascontent]').length) {
            if (props.info.gridPlace == GRIDPLACE_TYPE.FRONT) {
              !quesEl && (quesEl = item);
            } else {
              quesEl = item;
            }
          } else {
            quesEl = item;
          }
        }
      });
      if (props.item.scanMode == QUES_SCAN_MODE.AI_FILL || props.info.markType == MARK_TYPE.ONLINE) return;
      if (
        Paper.correctType == ICorrectType.WEB ||
        (Paper.ansToTopConfig.fill && Paper.isFillQues(props.item.typeId))
      )
        return;
      //0分不显示打分栏
      if (props.item.score == 0) return;
      let el = document.createElement('span');
      el.className = 'score-container noeditsave';
      // if (
      //   splitEls.length > 1 &&
      //   !(
      //     props.item.typeId == QUES_TYPE.subject &&
      //     props.info.markType == MARK_TYPE.YESORNO
      //   )
      // ) {
      //   el.className += " remove-ques";
      // }
      render(
        h(MarkScore, {
          typeId: props.item.typeId,
          judgeType: props.info.judgeType,
          'mark-type': props.info.markType,
          score: Number(props.item.score),
          step: props.info.step,
          isDecimal: props.info.decimal == DECIMAL_TYPE.HAVE,
          isSplit: props.info.gridType == GRID_TYPE.YES,
          id: props.item.id,
        }),
        el
      );
      if (props.item.typeId == QUES_TYPE.subject) {
        const content = quesEl.querySelector('div[class^="op"]');
        if (props.info.markType == MARK_TYPE.NUMBER) {
          el.classList.add('no-top');
          content?.insertBefore(el, content.firstElementChild);
        } else if (props.info.markType == MARK_TYPE.WRITE) {
          content?.insertBefore(el, content.firstElementChild);
        } else {
          content?.appendChild(el);
        }
      } else {
        el.style.display = 'inline-block';
        el.style.width = 'max-content';
        if (props.info.gridPlace == GRIDPLACE_TYPE.FRONT) {
          let content = quesEl?.getElementsByClassName('ques-num')[0];
          if (!content) {
            content = quesEl?.getElementsByClassName('fill-content')[0];
            content.parentElement?.insertBefore(el, content);
          } else {
            content.parentElement?.insertBefore(el, content.nextSibling);
          }
        } else {
          el.style.position = 'absolute';
          el.style.right = '0';
          // el.style.bottom = '0';
          let fillEl = quesEl?.querySelectorAll('[class^=hascontent]');
          if (fillEl.length == 0) {
            //兜底策略 避免标签被删除后无法附加打分栏
            fillEl = quesEl?.getElementsByClassName('ques-box');
          }
          const fillChildEl = fillEl[fillEl.length - 1];
          if (['P', 'DIV'].includes(fillChildEl?.lastElementChild?.nodeName)) {
            fillChildEl.lastElementChild.appendChild(el);
          } else {
            fillChildEl?.appendChild(el);
          }
        }
      }
    };

    const changeQuesHtml = (qid) => {
      if (qid != props.item.id) return;
      scanFill(false);
    }

    const scanChoice = () => {
      if (
        props.item.typeId != QUES_TYPE.choice &&
        props.item.typeId != QUES_TYPE.singleChoice
      ) {
        return;
      }
      renderOption();
      let els = document.querySelectorAll(`[id="${props.item.id}"]`);
      els.forEach(el => {
        $('.opts-write', el).html('(&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;)');
        $('.opts-write', el).css("padding", "");
        $('.opts-write', el).removeAttr("contenteditable");
        $('.opts-write', el).removeClass('opts-write');
      })
      if (Paper.ansType != ANSWER_TYPE.write) return;
      let elQuesContent;
      if (els.length > 1) {
        if (els[1].getElementsByClassName('ques-content').length) {
          elQuesContent = els[1];
        } else {
          elQuesContent = els[0]
        }
      } else {
        elQuesContent = els[0]
      }
      elQuesContent = elQuesContent.getElementsByClassName('ques-box')[elQuesContent.getElementsByClassName('ques-box').length - 1];
      let write = document.createElement('span');
      write.className = 'opts-write';
      write.style.padding = '2mm 0';
      write.setAttribute('contenteditable', 'false');
      write.innerHTML = "(&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;)";
      if ((/(?<!(\\)|(\\\())[(（]+(&nbsp;| |&ensp;|\s*)+[)）]/.test(elQuesContent.innerHTML))) {
        elQuesContent.innerHTML = elQuesContent.innerHTML.replace(/(?<!(\\)|(\\\())[(（]+(&nbsp;| |&ensp;|\s*)+[)）]/, write.outerHTML);
      } else {
        elQuesContent.getElementsByClassName('ques-content')[0].appendChild(write);
      }
    }
    /**
     * 识别填空题
    */
    const scanFill = (isFirst) => {
      if (!(props.item.typeId == QUES_TYPE.fill || props.item.typeId == QUES_TYPE.fillEva) || (Paper.ansToTopConfig.fill && props.item.hideQuesSurface == '1')) return;
      let lineList = [];
      let els = document.querySelectorAll(`[id="${props.item.id}"]`);
      Array.from(els)
        .forEach(el => {
          $('.fill-scan', el).css("background", "").css("padding", "");
          $('.fill-scan', el).removeClass('fill-scan');
          const curEle = el.getElementsByClassName('ques-box')[0];
          if (curEle) {
            let allTargetElements = curEle.querySelectorAll('u');
            let excludeContainer = curEle.querySelector('.tmpcontent');
            let uEles = [];
            if (Paper.isEnglishSubject()) {
              //如果是英语科目，则只扫描题目内容的u标签
              uEles = Array.from(allTargetElements).filter(element => {
                return !excludeContainer?.contains(element);
              });
            } else {
              uEles = Array.from(allTargetElements);
            }

            // let uEles = curEle.querySelectorAll('u')
            uEles.forEach((u, index) => {
              if ($(u).parents('.tmpcontent').length && Paper.isEnglishSubject()) return;
              let rect = u.getBoundingClientRect()
              const width = rect.width * window.EDIT_ZOOM;
              let nbsp = (u.innerHTML.match(/\s/g)?.length || 0) + (u.innerHTML.match(/&nbsp;/g)?.length || 0) * 2;
              if (pxConversionMm(width) >= 8 || nbsp > 6) {
                u.classList.add('fill-scan');
                if (props.item.markType == MARK_TYPE.ONLINE || props.item.scanMode == QUES_SCAN_MODE.AI_FILL) {
                  u.style.padding = '2mm 0';
                  u.style.background = 'rgb(233 233 233 / 50%)';
                }
                if (!props.item.lineList) {
                  props.item.lineList = [];
                }
                let line = props.item.lineList[index]
                if (line) {
                  line = Paper.createLineModel(line.width || '50mm', index, line);
                } else {
                  let score = '';
                  if (uEles.length == 1) {
                    score = props.item.score;
                  }
                  line = Paper.createLineModel('50mm', index);
                  line.score = score;
                }
                lineList.push(line);
              } else {
                u.classList.remove('fill-scan');
              }
            })
          }
        });
      if (!lineList.length) {
        if (!props.item.lineList) {
          props.item.lineList = [];
        }
        if (props.item.lineList.length) {
          lineList = props.item.lineList;
        } else {
          let line = props.item.lineList[0]
          if (!line) {
            line = Paper.createLineModel('50mm', 0);
            line.score = props.item.score
          }
          lineList.push(line);
        }
      }
      props.item.lineList = lineList;
      props.item.lineNum = lineList.length;
    };
    /**
     * 页面一开始加载
     */
    onMounted(() => {
      Paper.questions[props.item.id].qsList.forEach((v: any, i: Number) => {
        // v.showType = 4;
        if (props.item.typeId == QUES_TYPE.judge) {
          v.showType = 0;
        }
      });
      bus.on('setQueHeight', setQueHeight);
      bus.on('switchCorrect', renderScore);
      bus.on('renderOpts', renderOption);
      bus.on("changeQuesHtml", changeQuesHtml);
      bus.on("switchAnsType", scanChoice);
      Paper.on('changeLayout', initQuesHeight);
      renderScore();
      nextTick(() => {
        initEditor();
      });
      // if (state.ques.height) {
      //   setTimeout(() => {
      //     //延迟执行设置内容高度  避免跨页题目二次编辑后高度设置未分开 render函数重置高度
      //     state.height = `${Number(state.height.replace('mm', '')) - getSplitQuesH()}mm`;
      //   }, 5000);
      // }
    });
    onUnmounted(() => {
      let $dom = CKEDITOR.instances['ck_' + props.item.id];
      $dom && $dom.destroy();
      bus.off('setQueHeight', setQueHeight);
      bus.off('switchCorrect', renderScore);
      Paper.off('changeLayout', initQuesHeight);
      bus.off('renderOpts', renderOption);
      bus.off("changeQuesHtml", changeQuesHtml);
      bus.off("switchAnsType", scanChoice);
    });

    //获取当前题目对象，不包含分割的
    const getCurQuesDom = (id: string) => {
      const els = document.querySelectorAll(`[id="${id}"]`);
      return els[els.length - 1] as HTMLElement;
    };

    const getCurQuesContentH = () => {
      const el = getCurQuesDom(`ck_${props.item.id}`);
      let quesH = 0;
      Array.from(el?.children).forEach(ele => {
        if (!Array.from(ele.classList).includes('left')) {
          quesH += getHeight(ele);
        }
      });
      return quesH;
    };

    const getSplitQuesH = () => {
      let els = document.querySelectorAll(`[id="${props.item.id}"]`);
      let quesH = 0;
      Array.from(els)
        .splice(0, els.length - 1)
        .forEach(el => {
          quesH += getHeight(el.getElementsByClassName('ques-box')[0] || el);
        });
      return quesH;
    };

    const getQueHeight = () => {
      let quesH = getSplitQuesH();
      quesH += getHeight(getCurQuesDom(`ck_${props.item.id}`)) || 0;
      return quesH;
    };

    //初始化题目高度 layout 0:首次进入 !0：布局切换
    const initQuesHeight = (layout: number) => {
      renderOption();
      const ele = document.getElementById('ck_' + props.item.id);
      ele && (ele.style.height = '');
      state.initHeight = ele?.scrollHeight || 0;
      if (state.ques.height && layout == 0) {
        if ([IPAGELAYOUT.A32, IPAGELAYOUT.A23].includes(Paper.pageLayout)) {
          state.height = state.ques.height;
        } else {
          if (Paper.isSaveData) {
            state.height = state.ques.height;
          } else {
            state.height = `${Math.max(
              Number(state.ques.height.replace('mm', '')),
              pxConversionMm(state.initHeight)
            )}mm`;
          }
        }
        state.ques.height = state.height;
      } else {
        if (props.item.typeId == QUES_TYPE.subject) {
          state.height = `${pxConversionMm(state.initHeight + 90)}mm`;
          state.ques.height = state.height;
        }
        // else if(props.item.typeId == QUES_TYPE.fill || props.item.typeId == QUES_TYPE.fillEva) {
        //   state.height = `${pxConversionMm(state.initHeight + 18)}mm`;
        // }
        else {
          state.height = `${pxConversionMm(state.initHeight)}mm`;
          state.ques.height = state.height;
        }
      }
      instance!.proxy!.$forceUpdate();
    };

    const setQueHeight = () => {
      state.ques.height = `${getQueHeight()}mm`;
      // state.height =
      //   `${pxConversionMm(
      //     document.getElementById("ck_" + props.item.id)?.offsetHeight || 0
      //   )}mm` || state.height;
      // state.ques.height = state.height;
    };

    const initEditor = () => {
      // if(props.item.typeId == QUES_TYPE.fill || props.item.typeId == QUES_TYPE.judge){
      //   qContentRef.value.outerHTML = convertContent(
      //     state.ques.qsList[0].content
      //   );
      // }
      const ele = document.getElementById('ck_' + props.item.id);
      ele?.setAttribute('contenteditable', 'true');
      const editor = CKEDITOR.inline(ele);
      state.editor = editor;
      if (!editor) return;

      editor.on('contentDom', function (event: any) {
        event.editor.element.removeAttribute('title');
        // initQuesHeight(0);
        let count = Number(sessionStorage.getItem('queLoadCount'));
        sessionStorage.setItem('queLoadCount', (count + 1).toString());
        scanFill(true);
      });
      editor.on('paste', function (evt) {
        evt.data.dataValue = evt.data.dataValue
          .replace(/ques-option/gi, '')
          .replace(/q-r-o-item/gi, '');
      });
      editor.on('blur', function (e: any) {
        let el = document.getElementById('cke_ck_' + props.item.id);
        el && (el.style.display = 'none');
        if (calcEleIsHidden()) {
          ElMessage.error('有内容可能被遮挡，请确认');
          state.showErrorBox = true;
          setTimeout(() => {
            state.showErrorBox = false;
          }, 2000);
        }
        bus.emit('renderAllCard');
      });
      editor.on('change', function (event: any) {
        // 【偶现编辑器莫名在body前加入&ZeroWidthSpace占位字符】
        try {
          const htmlChildrenNodes = document.getElementById('nav')?.childNodes;
          if (htmlChildrenNodes) {
            const nodeLen = htmlChildrenNodes?.length || 0;
            for (let i = 0; i < nodeLen; i++) {
              const text = htmlChildrenNodes[i];
              if (text.nodeType && text.nodeType == 3) {
                text.textContent = text?.textContent?.replace(/[\u200B-\u200D\uFEFF]/g, '') || '';
              }
            }
          }
        } catch (e) {
          console.log(e);
        }
      });
    };

    /**
     * 计算指定标签是否被遮挡
     */
    const calcEleIsHidden = () => {
      const score = qWrapRef.value.getElementsByClassName('score-container');
      const quesBox = qWrapRef.value.getElementsByClassName('ques-box');
      //判断打分栏是否超出题目区域
      // if(isOverlap(score[0], quesBox[0])){
      //   return true;
      // }
      let imgs = qWrapRef.value.querySelectorAll('img');
      imgs = Array.prototype.filter.call(imgs, function (img) {
        //过滤选项中包含的图片
        return !img.closest('.ques-option');
      });
      let hasOverlap = false;
      if (imgs.length > 0) {
        try {
          imgs.forEach(img => {
            const opts = qWrapRef.value.getElementsByClassName('ocr-pos');
            const ques = qWrapRef.value.getElementsByClassName('ques-option');
            [...opts, ...ques, ...score].forEach(ele => {
              if (isOverlap(img, ele)) {
                hasOverlap = true;
                throw new Error('元素存在重叠');
              }
            });
          });
        } catch (e) { }
      }
      return hasOverlap;
    };
    /**
     * 判断两个元素是否重叠
     * @param {*} ele1
     * @param {*} ele2
     * @returns
     */
    const isOverlap = (ele1, ele2) => {
      const rect1 = ele1.getBoundingClientRect();
      const rect2 = ele2.getBoundingClientRect();
      return !(
        (
          rect1.right < rect2.left || // rect1在rect2左侧
          rect1.left > rect2.right || // rect1在rect2右侧
          rect1.bottom < rect2.top || // rect1在rect2上方
          rect1.top > rect2.bottom
        ) // rect1在rect2下方
      );
    };

    const getOptWidth = (showType: Number) => {
      return ``;
    };

    let startY = 0;
    let startHeight = 0;
    let newHeight = 0;
    function handleDrag(e: MouseEvent) {
      // let height = getConetntHeight();
      const deltaY = e.clientY - startY;
      newHeight = startHeight + deltaY;
      newHeight = newHeight < 2 ? 0 : newHeight;
      // console.log(deltaY, newHeight, initHeight);
      // if (newHeight >= 0 && newHeight > initHeight) {
      // if (pxConversionMm(newHeight) < height - 0) {
      state.height = `${pxConversionMm(newHeight)}mm`;
      state.ques.height = state.height;
      // }
      state.isDrag = true;
    }

    function stopDrag() {
      document.removeEventListener('mousemove', handleDrag);
      document.removeEventListener('mouseup', stopDrag);
      state.isDrag = false;
      const count = document.querySelectorAll(`[id="${props.item.id}"]`).length;
      // if (oldCount > 1) {
      //   state.height = `${pxConversionMm(newHeight)}mm`;
      //   nextTick(() => {
      //     bus.emit("renderAllCard");
      //   });
      // } else {
      if (!([IPAGELAYOUT.A32, IPAGELAYOUT.A23].includes(Paper.pageLayout) && count > 1)) {
        //如果为A32或A23异型排版 且跨页题目 不限制最低高度
        let height = Math.max(pxConversionMm(newHeight), getCurQuesContentH());
        state.height = `${height}mm`;
        state.ques.height = state.height;
        if(height == 0){
          //避免跨栏题目拖拽高度为0后，更新题目数据导致整题高度为0 
          //增加兜底策略，如果跨页题目高度为0，则延迟获取题目高度（取重新排版后的高度）
          setTimeout(() => {
            state.height = `${getCurQuesContentH()}mm`;
            state.ques.height = state.height;
          }, 200);
        }
      }

      nextTick(() => {
        bus.emit('renderAllCard');
        // nextTick(() => {
        //   const curCount = document.querySelectorAll(
        //     `[id="ck_${props.item.id}"]`
        //   ).length;
        //   if (curCount > oldCount) {
        //     state.height = `${getQueHeight() - getSplitQuesH()}mm`;
        //     nextTick(() => {
        //       bus.emit("renderAllCard");
        //     });
        //   }
        // });
      });
      // }
    }

    const startDrag = (e: MouseEvent) => {
      startY = e.clientY;
      // startHeight = qWrapRef.value.offsetHeight;
      startHeight = qWrapRef.value.offsetHeight;
      document.addEventListener('mousemove', handleDrag);
      document.addEventListener('mouseup', stopDrag);
    };
    /**
     * @name 拖动
     * @param item
     */
    const onMainOver = (e: MouseEvent) => {
      e.preventDefault();
      if (state.isFocus) return;
      state.isOver = true;
    };

    const onMainOut = (e: MouseEvent) => {
      e.preventDefault();
      if (state.isDrag) return;

      state.isOver = false;
    };

    const onWrapClick = (e: MouseEvent) => {
      e.preventDefault();
      if (state.isDrag) return;

      state.isEdit = true;
    };

    const onWrapFocus = (e: FocusEvent) => {
      e.preventDefault();
      if (state.isDrag) return;
      state.isOver = false;
      state.isFocus = true;
    };

    const onWrapBlur = (e: FocusEvent) => {
      e.preventDefault();
      state.isOver = false;
      state.isFocus = false;
      state.isEdit = false;
    };

    const editQues = () => {
      bus.emit('editQues', { item: props.info, index: props.index });
    };
    const showChange = () => {
      renderOption();
      //选择题更换排列 调用拖动事件，计算最小高度
      stopDrag();
    };

    const getConetntHeight = () => {
      //页面高度 - 首尾高度
      let height = pageHeight - footerKeepHeight * 2;
      const dom = getCurQuesDom(props.item.id);
      const page = parseInt(dom?.getAttribute('page') || '1');
      //当前页是否包含头部信息
      let hasInfo = true;
      if (Paper.pageLayout == IPAGELAYOUT.A4) {
        hasInfo = page % 2 == 1;
      } else if (Paper.pageLayout == IPAGELAYOUT.A3) {
        hasInfo = page % 4 == 1;
      } else {
        hasInfo = page % 6 == 1;
      }
      //减去头部信息高度
      if (hasInfo) {
        height -= getHeaderInfoHeight();
      }
      return height;
    };

    const setHeight = (type: string) => {
      const dom = getCurQuesDom(props.item.id);
      let height = getConetntHeight();
      let scoreH = 0;
      try {
        if (props.item.typeId == QUES_TYPE.subject) {
          const scoreEl = dom?.getElementsByClassName('score-container')[0];
          scoreH = getHeight(scoreEl);
        }
      } catch (error) { }
      if (type == 'half') {
        height = height / 2 - scoreH;
      } else {
        let _height = 0;
        const calcHeight = (_dom: any) => {
          if (_dom && _dom.className.indexOf('header-box') < 0) {
            _height += getHeight(_dom);
            calcHeight(_dom?.previousElementSibling);
          }
        };
        calcHeight(dom?.previousElementSibling);
        _height = Math.ceil(_height);
        height = pageHeight - footerKeepHeight * 2 - _height - scoreH;
        height = Math.max(height, pxConversionMm(dom?.offsetHeight) - scoreH);
        //避免精度差 冗余1mm
        // height = height - 2;
      }
      state.height = `${height}mm`;
      state.ques.height = state.height;
      instance!.proxy!.$forceUpdate();
      nextTick(() => {
        bus.emit('renderAllCard');
      });
    };

    const resetMath = () => {
      reloadMathtex(qWrapRef.value);
    };

    return {
      ...toRefs(state),
      onMainOver,
      onMainOut,
      onWrapFocus,
      onWrapBlur,
      onWrapClick,
      startDrag,
      getOptWidth,
      qWrapRef,
      qContentRef,
      setHeight,
      convertSort,
      hasTextorImg,
      converMathtex,
      convertContent,
      convertOption,
      confirmCreatCells,
      editQues,
      showChange,
      resetMath,
    };
  },
});
</script>

<style lang="scss" scoped>
@keyframes blink {
  0% {
    outline-color: red;
  }

  20% {
    outline-color: transparent;
  }

  40% {
    outline-color: red;
  }

  60% {
    outline-color: transparent;
  }

  80% {
    outline-color: red;
  }

  100% {
    outline-color: transparent;
  }
}

.q-opt {
  width: 100%;
  position: relative;

  &.error-tip {
    outline: 1mm solid transparent;
    animation: blink 1s infinite alternate;
  }

  .drag {
    pointer-events: none;
  }

  .edit {
    display: none;
    position: absolute;
    cursor: pointer;
    width: 24px;
    height: 24px;
    right: 0;
    top: -24px;
    background: url('../assets/icon_bianji.png') 100% 100%;
  }

  .up {
    top: auto;
    right: 30px;
    bottom: 100%;
    background: #f0f0f0;
    border-bottom: 1px solid;
    padding: 0 10px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
  }
}

.q-item-box6 .ques-box {
  border: 0.1mm solid #000;
}

.q-opt:hover {
  outline: 0.1mm solid #000;

  // border-radius: 1mm;
  .edit {
    display: block;
  }

  .pop {
    display: block;
  }
}

p {
  // display: flex;
  // align-items: center;
  // flex-wrap: wrap;
}
</style>
<style lang="scss">
.q-opt {
  clear: both;
}

/* .q-opt:hover + .edit {
  display: block;
}
.q-opt:hover + .pop {
  display: block;
} */

.ques-box {
  width: 100%;
  position: relative;
  padding: 0 1mm 0 1mm;

  &.cke_focus {
    box-shadow: inset 0 0 0 0.3mm #000;
  }

  img {
    vertical-align: middle;
    cursor: move;
    /* zoom: 0.8; */
  }

  .tb-dot {
    -webkit-text-emphasis: filled currentcolor;
    -webkit-text-emphasis-position: under right;
  }

  .tb-wave {
    background-image: url(../assets/wave.png);
    background-size: 11px 11px;
    background-position: 0 100%;
    background-repeat: repeat-x;
    padding-bottom: 6px;
    text-decoration: wavy;
  }

  table {
    display: inline-table;
  }

  .ck-math-tex.math-active {
    outline: 1px solid #409eff;
  }

  img:hover {
    box-shadow: 0 0 0 0.1mm #000 inset;
  }

  p {
    line-height: var(--lineHeight);
    min-height: var(--lineHeight);
  }
}

.image-inline {
  vertical-align: middle;
}

.q-opt .ques-box .nbsp {
  height: 100%;
  display: inline-block;
  font-family: Arial, Helvetica, sans-serif;
}

.q-opt .ques-box .left {
  float: left;
  clear: left;
  min-width: 5mm;
}

.q-opt .ques-box .q-r-option.has-none-title {
  width: calc(100% - 5mm);
}

.q-opt .ques-box .q-r-option {
  display: inline-block;
  width: 100%;
}

.ques-box .q-r-option .q-r-o-item {
  display: inline-block;
  /* align-items: center; */
}

/* .q-opt .ques-box p::after{
  color: #9c9c9c;
  content: '↵';
  margin-left: 1mm;
} */
// .q-opt .ques-box p>br:last-child {
//   display: none;
// }

.ocr-pos {
  &.n-opts {
    outline: unset;
  }

  outline: 2px solid red;
  display: inline-block;
  height: 3mm;
  line-height: 3mm;
  text-align: center;
  padding: 0 1mm;
  font-size: 2.6mm;
}

.ocr-pos .optionChar {
  padding: 0 0.6mm;
  margin: 0;
}

.q-r-o-img {
  /* display: flex;
  justify-content: right; */
}

.q-item-box .op0 .q-r-o-item {
  margin-right: 5mm;
}

.fontSizeTag {
  .ques-box {
    .n-opts {
      font-size: var(--fontSize);
    }
  }
}

.writing-container {
  display: inline-block;
  word-break: break-all;
  text-align: center;
  width: 100%;

  ~p {
    text-align: left;
  }
}

// .q-item-box .op1 .q-r-o-item {
//   width: calc(100%);
//   min-width: calc(100%);
//   max-width: calc(100%);
// }

// .q-item-box .op2 .q-r-o-item {
//   width: 48%;
//   min-width: 48%;
//   max-width: 48%;
// }

// .q-item-box .op3 .q-r-o-item {
//   width: 24%;
//   min-width: 24%;
//   max-width: 24%;
// }

// .q-item-box .op4 .q-r-o-item {
//   flex: 1;
//   width: 24%;
//   min-width: 24%;
//   max-width: 24%;
// }</style>
